# @ai-bot/embed

The AI Answer Bot embed widget is a React TypeScript component that provides an intelligent chatbot interface for websites. It features modern styling with customizable branding, smart content suggestions, and seamless integration capabilities.

## Features

- **Modern Design**: Clean white/off-white gradient styling with smooth animations
- **Customizable Branding**: Logo, colors, and text can be configured per client
- **Smart Suggestions**: AI-powered question suggestions based on scraped content
- **Link Previews**: Contextual page recommendations with descriptions
- **Responsive**: Works seamlessly on desktop and mobile devices
- **Easy Integration**: Simple embed code or React component usage

## Installation

```bash
npm install @ai-bot/embed
```

## Usage

### As React Component

```tsx
import { Widget } from '@ai-bot/embed';

const config = {
  clientId: 'your-client-id',
  apiUrl: 'https://your-api.com',
  primaryColor: '#667eea',
  askAnythingText: 'How can we help?',
  logo: 'https://your-site.com/logo.png',
  placeholder: 'Ask us anything...',
  siteName: 'Your Site'
};

function App() {
  return (
    <div>
      <Widget 
        config={config}
        onMessage={(message) => console.log('User asked:', message)}
        onSuggestionClick={(suggestion) => console.log('Suggestion clicked:', suggestion)}
        onLinkClick={(link) => console.log('Link clicked:', link)}
      />
    </div>
  );
}
```

### As Embed Script

```html
<script src="https://cdn.your-domain.com/ai-answer-bot.umd.js"></script>
<script>
  window.AIAnswerBot.init({
    clientId: 'your-client-id',
    apiUrl: 'https://your-api.com',
    primaryColor: '#667eea',
    askAnythingText: 'How can we help?',
    logo: 'https://your-site.com/logo.png'
  });
</script>
```

## Configuration

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `clientId` | string | Yes | Unique identifier for your bot instance |
| `apiUrl` | string | Yes | Backend API endpoint URL |
| `primaryColor` | string | No | Custom brand color (hex format) |
| `askAnythingText` | string | No | Header text (default: "Ask anything") |
| `logo` | string | No | Logo image URL |
| `placeholder` | string | No | Input placeholder text |
| `siteName` | string | No | Site name for accessibility |

## Development

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Run type checking
npm run type-check

# Run linting
npm run lint
```

## API Integration

The widget communicates with the backend API for:
- Fetching initial suggestions and link previews
- Processing user questions and generating responses
- Tracking user interactions for analytics

See the API documentation for endpoint details and authentication requirements.
