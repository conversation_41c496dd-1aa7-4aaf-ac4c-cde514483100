/**
 * API utility functions for the AI Answer Bot embed widget.
 * This module handles all communication with the backend API, including
 * fetching bot responses, suggestions, and link previews with proper error handling.
 */

import { BotResponse, BotSuggestion, LinkPreview } from '../types';

export class ApiError extends Error {
  constructor(message: string, public status?: number) {
    super(message);
    this.name = 'ApiError';
  }
}

const handleApiResponse = async (response: Response) => {
  if (!response.ok) {
    const errorText = await response.text().catch(() => 'Unknown error');
    throw new ApiError(`API request failed: ${errorText}`, response.status);
  }
  return response.json();
};

export const fetchBotResponse = async (
  question: string, 
  clientId: string, 
  apiUrl: string
): Promise<BotResponse> => {
  try {
    const response = await fetch(`${apiUrl}/api/bot/ask`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        question,
        clientId
      })
    });

    const data = await handleApiResponse(response);
    
    return {
      id: data.id || Date.now().toString(),
      answer: data.answer || 'Sorry, I couldn\'t process your question.',
      suggestions: data.suggestions || [],
      linkPreviews: data.linkPreviews || [],
      timestamp: new Date(data.timestamp || Date.now())
    };
  } catch (error) {
    console.error('Failed to fetch bot response:', error);
    throw error;
  }
};

export const fetchSuggestions = async (
  clientId: string, 
  apiUrl: string
): Promise<BotSuggestion[]> => {
  try {
    const response = await fetch(`${apiUrl}/api/suggestions/${clientId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    const data = await handleApiResponse(response);
    return data.suggestions || [];
  } catch (error) {
    console.error('Failed to fetch suggestions:', error);
    // Return fallback suggestions
    return [
      { id: '1', question: 'How can I get started?' },
      { id: '2', question: 'What services do you offer?' },
      { id: '3', question: 'How do I contact support?' }
    ];
  }
};

export const fetchLinkPreviews = async (
  clientId: string, 
  apiUrl: string
): Promise<LinkPreview[]> => {
  try {
    const response = await fetch(`${apiUrl}/api/links/${clientId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    const data = await handleApiResponse(response);
    return data.links || [];
  } catch (error) {
    console.error('Failed to fetch link previews:', error);
    // Return empty array as fallback
    return [];
  }
};

export const trackInteraction = async (
  clientId: string,
  apiUrl: string,
  type: 'question' | 'suggestion' | 'link',
  data: any
): Promise<void> => {
  try {
    await fetch(`${apiUrl}/api/analytics/track`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        clientId,
        type,
        data,
        timestamp: new Date().toISOString()
      })
    });
  } catch (error) {
    // Analytics tracking failures should not break the widget
    console.warn('Failed to track interaction:', error);
  }
};
