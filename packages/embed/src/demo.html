<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Answer Bot Demo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .demo-container {
            max-width: 1200px;
            width: 100%;
            padding: 20px;
            position: relative;
        }
        
        .website-mockup {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            position: relative;
        }
        
        .browser-bar {
            background: #f1f3f4;
            height: 40px;
            display: flex;
            align-items: center;
            padding: 0 16px;
            border-bottom: 1px solid #e8eaed;
        }
        
        .browser-dots {
            display: flex;
            gap: 8px;
        }
        
        .browser-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .dot-red { background: #ff5f57; }
        .dot-yellow { background: #ffbd2e; }
        .dot-green { background: #28ca42; }
        
        .address-bar {
            background: white;
            border: 1px solid #e8eaed;
            border-radius: 20px;
            padding: 8px 16px;
            margin-left: 20px;
            flex: 1;
            max-width: 300px;
            font-size: 14px;
            color: #5f6368;
        }
        
        .website-content {
            padding: 40px;
            min-height: 500px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><rect width="1200" height="600" fill="%23f8f9fa"/><rect x="50" y="50" width="300" height="40" fill="%23e9ecef" rx="4"/><rect x="50" y="120" width="500" height="20" fill="%23dee2e6" rx="2"/><rect x="50" y="160" width="400" height="20" fill="%23dee2e6" rx="2"/><rect x="50" y="200" width="450" height="20" fill="%23dee2e6" rx="2"/><rect x="50" y="280" width="200" height="30" fill="%23667eea" rx="4"/><rect x="700" y="50" width="450" height="300" fill="%23e9ecef" rx="8"/></svg>') no-repeat center;
            background-size: cover;
        }
        
        .demo-title {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .demo-title h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0 0 10px 0;
        }
        
        .demo-title p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 0;
        }
        
        @media (max-width: 768px) {
            .demo-container {
                padding: 10px;
            }
            
            .website-content {
                padding: 20px;
                min-height: 400px;
            }
            
            .demo-title h1 {
                font-size: 2rem;
            }
            
            .demo-title p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-title">
            <h1>AI Answer Bot Demo</h1>
            <p>Experience intelligent customer support with customizable branding</p>
        </div>
        
        <div class="website-mockup">
            <div class="browser-bar">
                <div class="browser-dots">
                    <div class="browser-dot dot-red"></div>
                    <div class="browser-dot dot-yellow"></div>
                    <div class="browser-dot dot-green"></div>
                </div>
                <div class="address-bar">https://demo-website.com</div>
            </div>
            
            <div class="website-content">
                <!-- Website mockup content -->
            </div>
        </div>
    </div>

    <!-- AI Answer Bot Embed -->
    <script>
        // Demo configuration
        const botConfig = {
            clientId: 'demo-client-id',
            apiUrl: 'http://localhost:5000/api',
            primaryColor: '#667eea',
            askAnythingText: 'How can we help?',
            logo: 'https://via.placeholder.com/40x40/667eea/ffffff?text=D',
            placeholder: 'Ask us anything about our demo...',
            siteName: 'Demo Website'
        };

        // Initialize bot when the embed script loads
        // In production, this would be loaded from CDN
        // window.AIAnswerBot.init(botConfig);
        
        // For demo purposes, we'll show a placeholder
        console.log('AI Answer Bot would be initialized with config:', botConfig);
    </script>
</body>
</html>
