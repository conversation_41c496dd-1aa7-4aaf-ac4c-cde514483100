/**
 * CSS Module for the main AI Answer Bot widget component.
 * Provides modern styling with subtle gradients, smooth animations, and responsive design.
 * The widget features a clean white/off-white aesthetic with customizable primary colors.
 */

.widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 10000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.widgetButton {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.widgetButton:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

.chatContainer {
  position: absolute;
  bottom: 80px;
  right: 0;
  width: 380px;
  height: 500px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.3s ease;
}

.chatContainer.open {
  transform: translateY(0);
  opacity: 1;
}

.chatHeader {
  background: linear-gradient(135deg, #ffffff 0%, #f1f3f4 100%);
  padding: 20px;
  border-bottom: 1px solid #e8eaed;
  text-align: center;
}

.askAnything {
  font-size: 18px;
  font-weight: 600;
  color: #202124;
  margin: 0 0 12px 0;
}

.logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  margin: 0 auto 16px;
  display: block;
}

.inputContainer {
  padding: 0 20px 16px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e8eaed;
  border-radius: 24px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
  background: white;
}

.input:focus {
  border-color: var(--primary-color, #667eea);
}

.input::placeholder {
  color: #9aa0a6;
}

.content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.suggestions {
  margin-bottom: 20px;
}

.suggestionItem {
  background: white;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 12px 16px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #202124;
}

.suggestionItem:hover {
  background: #f8f9fa;
  border-color: var(--primary-color, #667eea);
  transform: translateY(-1px);
}

.linkPreviews {
  margin-top: 16px;
}

.linkPreview {
  background: white;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.linkPreview:hover {
  background: #f8f9fa;
  border-color: var(--primary-color, #667eea);
  transform: translateY(-1px);
}

.linkThumbnail {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  flex-shrink: 0;
}

.linkContent {
  flex: 1;
}

.linkTitle {
  font-size: 14px;
  font-weight: 600;
  color: #202124;
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.linkDescription {
  font-size: 12px;
  color: #5f6368;
  margin: 0;
  line-height: 1.4;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #5f6368;
}

@media (max-width: 480px) {
  .widget {
    bottom: 10px;
    right: 10px;
  }
  
  .chatContainer {
    width: calc(100vw - 20px);
    height: calc(100vh - 100px);
    right: -10px;
  }
}
