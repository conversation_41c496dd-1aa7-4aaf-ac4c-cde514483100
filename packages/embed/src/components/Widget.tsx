/**
 * Main AI Answer Bot widget component.
 * This component renders the floating chat widget with customizable branding,
 * smart suggestions, and link previews. It handles user interactions and API communication.
 */

import React, { useState, useEffect, useRef } from 'react';
import { EmbedProps, WidgetState, BotSuggestion, LinkPreview, ChatMessage } from '../types';
import { fetchBotResponse, fetchSuggestions, fetchLinkPreviews } from '../utils/api';
import styles from '../styles/Widget.module.css';

const Widget: React.FC<EmbedProps> = ({ 
  config, 
  onMessage, 
  onSuggestionClick, 
  onLinkClick 
}) => {
  const [state, setState] = useState<WidgetState>({
    isOpen: false,
    isLoading: false,
    messages: [],
    currentInput: ''
  });

  const [suggestions, setSuggestions] = useState<BotSuggestion[]>([]);
  const [linkPreviews, setLinkPreviews] = useState<LinkPreview[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);

  // Load initial suggestions and link previews
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const [suggestionsData, linksData] = await Promise.all([
          fetchSuggestions(config.clientId, config.apiUrl),
          fetchLinkPreviews(config.clientId, config.apiUrl)
        ]);
        setSuggestions(suggestionsData);
        setLinkPreviews(linksData);
      } catch (error) {
        console.error('Failed to load initial data:', error);
      }
    };

    loadInitialData();
  }, [config.clientId, config.apiUrl]);

  // Apply custom primary color
  useEffect(() => {
    if (config.primaryColor) {
      document.documentElement.style.setProperty('--primary-color', config.primaryColor);
    }
  }, [config.primaryColor]);

  const toggleWidget = () => {
    setState(prev => ({ ...prev, isOpen: !prev.isOpen }));
    if (!state.isOpen && inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setState(prev => ({ ...prev, currentInput: e.target.value }));
  };

  const handleSubmit = async (question?: string) => {
    const messageText = question || state.currentInput.trim();
    if (!messageText) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: messageText,
      timestamp: new Date()
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      currentInput: '',
      isLoading: true
    }));

    onMessage?.(messageText);

    try {
      const response = await fetchBotResponse(messageText, config.clientId, config.apiUrl);
      
      const botMessage: ChatMessage = {
        id: response.id,
        type: 'bot',
        content: response.answer,
        timestamp: response.timestamp,
        suggestions: response.suggestions,
        linkPreviews: response.linkPreviews
      };

      setState(prev => ({
        ...prev,
        messages: [...prev.messages, botMessage],
        isLoading: false
      }));
    } catch (error) {
      console.error('Failed to get bot response:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit();
    }
  };

  const handleSuggestionClick = (suggestion: BotSuggestion) => {
    onSuggestionClick?.(suggestion);
    handleSubmit(suggestion.question);
  };

  const handleLinkClick = (link: LinkPreview) => {
    onLinkClick?.(link);
    window.open(link.url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className={styles.widget}>
      <button 
        className={styles.widgetButton}
        onClick={toggleWidget}
        aria-label="Open AI Answer Bot"
      >
        💬
      </button>

      <div className={`${styles.chatContainer} ${state.isOpen ? styles.open : ''}`}>
        <div className={styles.chatHeader}>
          <h3 className={styles.askAnything}>
            {config.askAnythingText || 'Ask anything'}
          </h3>
          {config.logo && (
            <img 
              src={config.logo} 
              alt={`${config.siteName || 'Site'} logo`}
              className={styles.logo}
            />
          )}
        </div>

        <div className={styles.inputContainer}>
          <input
            ref={inputRef}
            type="text"
            className={styles.input}
            placeholder={config.placeholder || 'Type your question here...'}
            value={state.currentInput}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            disabled={state.isLoading}
          />
        </div>

        <div className={styles.content}>
          {state.messages.length === 0 && (
            <>
              {suggestions.length > 0 && (
                <div className={styles.suggestions}>
                  {suggestions.map(suggestion => (
                    <div
                      key={suggestion.id}
                      className={styles.suggestionItem}
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      {suggestion.question}
                    </div>
                  ))}
                </div>
              )}

              {linkPreviews.length > 0 && (
                <div className={styles.linkPreviews}>
                  {linkPreviews.map(link => (
                    <div
                      key={link.id}
                      className={styles.linkPreview}
                      onClick={() => handleLinkClick(link)}
                    >
                      {link.thumbnail && (
                        <img 
                          src={link.thumbnail} 
                          alt=""
                          className={styles.linkThumbnail}
                        />
                      )}
                      <div className={styles.linkContent}>
                        <h4 className={styles.linkTitle}>{link.title}</h4>
                        <p className={styles.linkDescription}>{link.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}

          {state.isLoading && (
            <div className={styles.loading}>
              Thinking...
            </div>
          )}

          {/* Chat messages would be rendered here */}
        </div>
      </div>
    </div>
  );
};

export default Widget;
