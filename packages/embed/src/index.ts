/**
 * Main entry point for the AI Answer Bot embed widget.
 * This file exports the Widget component and provides a simple initialization function
 * for easy integration into any website via script tag or module import.
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import Widget from './components/Widget';
import { BotConfig } from './types';

export { Widget };
export * from './types';

// Global initialization function for script tag integration
declare global {
  interface Window {
    AIAnswerBot: {
      init: (config: BotConfig, containerId?: string) => void;
      destroy: (containerId?: string) => void;
    };
  }
}

const initializeBot = (config: BotConfig, containerId: string = 'ai-answer-bot') => {
  let container = document.getElementById(containerId);
  
  if (!container) {
    container = document.createElement('div');
    container.id = containerId;
    document.body.appendChild(container);
  }

  const root = ReactDOM.createRoot(container);
  root.render(React.createElement(Widget, { config }));
  
  return root;
};

const destroyBot = (containerId: string = 'ai-answer-bot') => {
  const container = document.getElementById(containerId);
  if (container) {
    container.remove();
  }
};

// Make available globally for script tag usage
if (typeof window !== 'undefined') {
  window.AIAnswerBot = {
    init: initializeBot,
    destroy: destroyBot
  };
}

export { initializeBot, destroyBot };
