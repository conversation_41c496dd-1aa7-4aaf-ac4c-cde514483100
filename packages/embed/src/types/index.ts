/**
 * Type definitions for the AI Answer Bot embed widget.
 * This file defines all the interfaces and types used throughout the embed widget,
 * including client configuration, bot responses, and component props.
 */

export interface BotConfig {
  clientId: string;
  apiUrl: string;
  primaryColor?: string;
  askAnythingText?: string;
  logo?: string;
  placeholder?: string;
  siteName?: string;
}

export interface BotSuggestion {
  id: string;
  question: string;
  category?: string;
}

export interface LinkPreview {
  id: string;
  url: string;
  title: string;
  description: string;
  thumbnail?: string;
}

export interface BotResponse {
  id: string;
  answer: string;
  suggestions: BotSuggestion[];
  linkPreviews: LinkPreview[];
  timestamp: Date;
}

export interface ChatMessage {
  id: string;
  type: 'user' | 'bot';
  content: string;
  timestamp: Date;
  suggestions?: BotSuggestion[];
  linkPreviews?: LinkPreview[];
}

export interface EmbedProps {
  config: BotConfig;
  onMessage?: (message: string) => void;
  onSuggestionClick?: (suggestion: BotSuggestion) => void;
  onLinkClick?: (link: LinkPreview) => void;
}

export interface WidgetState {
  isOpen: boolean;
  isLoading: boolean;
  messages: ChatMessage[];
  currentInput: string;
}

export type WidgetPosition = 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
