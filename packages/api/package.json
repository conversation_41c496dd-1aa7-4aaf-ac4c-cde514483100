{"name": "@ai-bot/api", "version": "1.0.0", "description": "AI Answer Bot API - Node.js TypeScript backend with PostgreSQL for client management and bot functionality", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "migrate": "tsx src/database/migrate.ts", "seed": "tsx src/database/seed.ts", "test": "jest", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "pg": "^8.11.3", "dotenv": "^16.3.1", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1", "axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "rate-limiter-flexible": "^4.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/pg": "^8.10.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "@types/node": "^20.10.0", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.0"}}