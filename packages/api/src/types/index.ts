/**
 * Type definitions for the AI Answer Bot API.
 * This file contains all TypeScript interfaces and types used throughout the backend,
 * including database models, API request/response types, and service interfaces.
 */

export interface Client {
  id: string;
  name: string;
  domain: string;
  apiKey: string;
  config: ClientConfig;
  createdAt: Date;
  updatedAt: Date;
}

export interface ClientConfig {
  primaryColor?: string;
  askAnythingText?: string;
  logo?: string;
  placeholder?: string;
  siteName?: string;
}

export interface ScrapedPage {
  id: string;
  clientId: string;
  url: string;
  title: string;
  content: string;
  description?: string;
  thumbnail?: string;
  lastScraped: Date;
  isActive: boolean;
}

export interface BotSuggestion {
  id: string;
  clientId: string;
  question: string;
  category?: string;
  priority: number;
  isActive: boolean;
}

export interface BotResponse {
  id: string;
  question: string;
  answer: string;
  suggestions: BotSuggestion[];
  linkPreviews: LinkPreview[];
  timestamp: Date;
}

export interface LinkPreview {
  id: string;
  url: string;
  title: string;
  description: string;
  thumbnail?: string;
}

export interface Analytics {
  id: string;
  clientId: string;
  type: 'question' | 'suggestion' | 'link';
  data: any;
  timestamp: Date;
  userAgent?: string;
  ipAddress?: string;
}

// API Request/Response Types
export interface CreateClientRequest {
  name: string;
  domain: string;
  config?: ClientConfig;
}

export interface UpdateClientRequest {
  name?: string;
  domain?: string;
  config?: ClientConfig;
}

export interface AddScrapedPageRequest {
  url: string;
  title?: string;
  description?: string;
}

export interface BotAskRequest {
  question: string;
  clientId: string;
}

export interface BotAskResponse {
  id: string;
  answer: string;
  suggestions: BotSuggestion[];
  linkPreviews: LinkPreview[];
  timestamp: string;
}

export interface SuggestionsResponse {
  suggestions: BotSuggestion[];
}

export interface LinkPreviewsResponse {
  links: LinkPreview[];
}

export interface TrackAnalyticsRequest {
  clientId: string;
  type: 'question' | 'suggestion' | 'link';
  data: any;
  timestamp: string;
}

// Database Query Types
export interface DatabaseClient {
  id: string;
  name: string;
  domain: string;
  api_key: string;
  config: string; // JSON string
  created_at: Date;
  updated_at: Date;
}

export interface DatabaseScrapedPage {
  id: string;
  client_id: string;
  url: string;
  title: string;
  content: string;
  description?: string;
  thumbnail?: string;
  last_scraped: Date;
  is_active: boolean;
}

export interface DatabaseBotSuggestion {
  id: string;
  client_id: string;
  question: string;
  category?: string;
  priority: number;
  is_active: boolean;
}

export interface DatabaseAnalytics {
  id: string;
  client_id: string;
  type: string;
  data: string; // JSON string
  timestamp: Date;
  user_agent?: string;
  ip_address?: string;
}
