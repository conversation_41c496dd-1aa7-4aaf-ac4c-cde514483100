/**
 * Type definitions for the AI Answer Bot admin panel.
 * This file contains all TypeScript interfaces and types used throughout the admin interface,
 * including client management, configuration, and API response types.
 */

export interface Client {
  id: string;
  name: string;
  domain: string;
  apiKey: string;
  config: ClientConfig;
  createdAt: string;
  updatedAt: string;
}

export interface ClientConfig {
  primaryColor?: string;
  askAnythingText?: string;
  logo?: string;
  placeholder?: string;
  siteName?: string;
}

export interface CreateClientRequest {
  name: string;
  domain: string;
  config?: ClientConfig;
}

export interface UpdateClientRequest {
  name?: string;
  domain?: string;
  config?: ClientConfig;
}

export interface ScrapedPage {
  id: string;
  clientId: string;
  url: string;
  title: string;
  content: string;
  description?: string;
  thumbnail?: string;
  lastScraped: string;
  isActive: boolean;
}

export interface AddScrapedPageRequest {
  url: string;
  title?: string;
  description?: string;
}

export interface BotSuggestion {
  id: string;
  clientId: string;
  question: string;
  category?: string;
  priority: number;
  isActive: boolean;
}

export interface CreateSuggestionRequest {
  question: string;
  category?: string;
  priority?: number;
}

export interface Analytics {
  id: string;
  clientId: string;
  type: 'question' | 'suggestion' | 'link';
  data: any;
  timestamp: string;
  userAgent?: string;
  ipAddress?: string;
}

export interface AnalyticsStats {
  totalInteractions: number;
  questionsAsked: number;
  suggestionsClicked: number;
  linksClicked: number;
  topQuestions: Array<{
    question: string;
    count: number;
  }>;
  dailyStats: Array<{
    date: string;
    interactions: number;
  }>;
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form Types
export interface ClientFormData {
  name: string;
  domain: string;
  primaryColor: string;
  askAnythingText: string;
  logo: string;
  placeholder: string;
  siteName: string;
}

export interface ScrapedPageFormData {
  url: string;
  title: string;
  description: string;
}

export interface SuggestionFormData {
  question: string;
  category: string;
  priority: number;
}
