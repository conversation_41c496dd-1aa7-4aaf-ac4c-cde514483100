/**
 * Analytics page component for the admin panel.
 * This component displays comprehensive analytics and reporting for bot usage,
 * user interactions, and performance metrics with filtering and export capabilities.
 */

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  BarChart3, 
  TrendingUp, 
  MessageSquare, 
  ExternalLink,
  Download,
  Calendar,
  Filter
} from 'lucide-react';
import { apiService } from '../services/api';

const Analytics: React.FC = () => {
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });

  const { data: clients } = useQuery({
    queryKey: ['clients'],
    queryFn: () => apiService.getClients(1, 100)
  });

  const { data: analytics } = useQuery({
    queryKey: ['analytics', selectedClient, dateRange],
    queryFn: () => apiService.getAnalytics(selectedClient, dateRange.start, dateRange.end),
    enabled: !!selectedClient
  });

  const mockStats = [
    {
      name: 'Total Interactions',
      value: analytics?.totalInteractions || 1234,
      change: '+12%',
      changeType: 'positive' as const,
      icon: TrendingUp
    },
    {
      name: 'Questions Asked',
      value: analytics?.questionsAsked || 856,
      change: '+8%',
      changeType: 'positive' as const,
      icon: MessageSquare
    },
    {
      name: 'Suggestions Clicked',
      value: analytics?.suggestionsClicked || 234,
      change: '+15%',
      changeType: 'positive' as const,
      icon: BarChart3
    },
    {
      name: 'Links Clicked',
      value: analytics?.linksClicked || 144,
      change: '+5%',
      changeType: 'positive' as const,
      icon: ExternalLink
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
          <p className="mt-1 text-sm text-gray-500">
            Monitor bot performance and user engagement
          </p>
        </div>
        <button className="btn-secondary">
          <Download className="w-4 h-4 mr-2" />
          Export Data
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Client
            </label>
            <select
              value={selectedClient}
              onChange={(e) => setSelectedClient(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">All clients</option>
              {clients?.data?.map((client) => (
                <option key={client.id} value={client.id}>
                  {client.name}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Start Date
            </label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              End Date
            </label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {mockStats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Icon className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.name}
                      </dt>
                      <dd className="flex items-baseline">
                        <div className="text-2xl font-semibold text-gray-900">
                          {stat.value.toLocaleString()}
                        </div>
                        <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                          stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {stat.change}
                        </div>
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Charts and Detailed Analytics */}
      {selectedClient && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Questions */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Top Questions</h3>
            <div className="space-y-3">
              {analytics?.topQuestions?.map((question, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm text-gray-900 truncate flex-1">
                    {question.question}
                  </span>
                  <span className="text-sm text-gray-500 ml-4">
                    {question.count}
                  </span>
                </div>
              )) || (
                <p className="text-sm text-gray-500">No data available</p>
              )}
            </div>
          </div>

          {/* Daily Activity */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Daily Activity</h3>
            <div className="h-64 flex items-end justify-between space-x-1">
              {analytics?.dailyStats?.map((day, index) => (
                <div
                  key={index}
                  className="bg-primary-500 rounded-t"
                  style={{
                    height: `${Math.max(4, (day.interactions / Math.max(...(analytics.dailyStats?.map(d => d.interactions) || [1]))) * 100)}%`,
                    width: '100%'
                  }}
                  title={`${day.date}: ${day.interactions} interactions`}
                ></div>
              )) || (
                <p className="text-sm text-gray-500 w-full text-center">No data available</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* No Client Selected State */}
      {!selectedClient && (
        <div className="bg-white shadow rounded-lg p-12 text-center">
          <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">Select a client</h3>
          <p className="mt-1 text-sm text-gray-500">
            Choose a client from the dropdown above to view detailed analytics.
          </p>
        </div>
      )}
    </div>
  );
};

export default Analytics;
