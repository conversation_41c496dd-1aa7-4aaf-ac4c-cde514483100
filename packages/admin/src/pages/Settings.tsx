/**
 * Settings page component for the admin panel.
 * This component provides system-wide configuration options including
 * API settings, security configuration, and system maintenance tools.
 */

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Settings as SettingsIcon, 
  Shield, 
  Database, 
  Server,
  Key,
  RefreshCw
} from 'lucide-react';
import { apiService } from '../services/api';

const Settings: React.FC = () => {
  const { data: healthStatus, refetch: refetchHealth } = useQuery({
    queryKey: ['health'],
    queryFn: () => apiService.healthCheck()
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Settings</h1>
        <p className="mt-1 text-sm text-gray-500">
          Manage system configuration and security settings
        </p>
      </div>

      {/* System Status */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Server className="w-5 h-5 text-primary-500 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">System Status</h3>
          </div>
          <button
            onClick={() => refetchHealth()}
            className="btn-secondary"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">API Server</span>
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${
                  healthStatus?.status === 'ok' ? 'bg-green-400' : 'bg-red-400'
                }`}></div>
                <span className="text-sm text-gray-900">
                  {healthStatus?.status === 'ok' ? 'Online' : 'Offline'}
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Database</span>
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-2 ${
                  healthStatus?.database === 'connected' ? 'bg-green-400' : 'bg-red-400'
                }`}></div>
                <span className="text-sm text-gray-900">
                  {healthStatus?.database === 'connected' ? 'Connected' : 'Disconnected'}
                </span>
              </div>
            </div>
          </div>
          
          <div>
            <p className="text-sm text-gray-500">
              Last checked: {healthStatus?.timestamp ? new Date(healthStatus.timestamp).toLocaleString() : 'Never'}
            </p>
          </div>
        </div>
      </div>

      {/* Security Settings */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center mb-4">
          <Shield className="w-5 h-5 text-primary-500 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">Security</h3>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-gray-900">Admin API Key</h4>
              <p className="text-sm text-gray-500">Used for admin panel authentication</p>
            </div>
            <button className="btn-secondary">
              <Key className="w-4 h-4 mr-2" />
              Regenerate
            </button>
          </div>

          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-gray-900">Rate Limiting</h4>
              <p className="text-sm text-gray-500">100 requests per minute per IP</p>
            </div>
            <span className="text-sm text-green-600 font-medium">Active</span>
          </div>

          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-gray-900">CORS Configuration</h4>
              <p className="text-sm text-gray-500">Cross-origin request settings</p>
            </div>
            <button className="btn-secondary">Configure</button>
          </div>
        </div>
      </div>

      {/* Database Management */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center mb-4">
          <Database className="w-5 h-5 text-primary-500 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">Database Management</h3>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-gray-900">Run Migrations</h4>
              <p className="text-sm text-gray-500">Update database schema to latest version</p>
            </div>
            <button className="btn-secondary">
              <RefreshCw className="w-4 h-4 mr-2" />
              Migrate
            </button>
          </div>

          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-gray-900">Clear Cache</h4>
              <p className="text-sm text-gray-500">Clear cached bot responses</p>
            </div>
            <button className="btn-secondary">Clear</button>
          </div>

          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div>
              <h4 className="text-sm font-medium text-gray-900">Backup Database</h4>
              <p className="text-sm text-gray-500">Create a backup of all data</p>
            </div>
            <button className="btn-secondary">Backup</button>
          </div>
        </div>
      </div>

      {/* System Information */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center mb-4">
          <SettingsIcon className="w-5 h-5 text-primary-500 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">System Information</h3>
        </div>

        <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <dt className="text-sm font-medium text-gray-500">Version</dt>
            <dd className="text-sm text-gray-900">1.0.0</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Environment</dt>
            <dd className="text-sm text-gray-900">Production</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Node.js Version</dt>
            <dd className="text-sm text-gray-900">18.17.0</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-gray-500">Database Version</dt>
            <dd className="text-sm text-gray-900">PostgreSQL 15.4</dd>
          </div>
        </dl>
      </div>
    </div>
  );
};

export default Settings;
