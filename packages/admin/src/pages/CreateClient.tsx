/**
 * Create client page component for the admin panel.
 * This component provides a comprehensive form for creating new clients with
 * brand customization, domain configuration, and initial bot settings.
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Save, Eye } from 'lucide-react';
import { apiService } from '../services/api';
import { ClientFormData } from '../types';
import toast from 'react-hot-toast';

const clientSchema = z.object({
  name: z.string().min(1, 'Client name is required').max(100, 'Name too long'),
  domain: z.string().min(1, 'Domain is required').regex(/^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/, 'Invalid domain format'),
  primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color').default('#667eea'),
  askAnythingText: z.string().max(50, 'Text too long').default('Ask anything'),
  logo: z.string().url('Invalid URL').optional().or(z.literal('')),
  placeholder: z.string().max(100, 'Placeholder too long').default('Type your question here...'),
  siteName: z.string().max(50, 'Site name too long').optional().or(z.literal(''))
});

const CreateClient: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isValid }
  } = useForm<ClientFormData>({
    resolver: zodResolver(clientSchema),
    defaultValues: {
      primaryColor: '#667eea',
      askAnythingText: 'Ask anything',
      placeholder: 'Type your question here...'
    }
  });

  const createClientMutation = useMutation({
    mutationFn: (data: ClientFormData) => {
      const { name, domain, ...config } = data;
      return apiService.createClient({
        name,
        domain,
        config: {
          primaryColor: config.primaryColor,
          askAnythingText: config.askAnythingText,
          logo: config.logo || undefined,
          placeholder: config.placeholder,
          siteName: config.siteName || undefined
        }
      });
    },
    onSuccess: (client) => {
      toast.success('Client created successfully');
      queryClient.invalidateQueries({ queryKey: ['clients'] });
      navigate(`/clients/${client.id}`);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to create client');
    }
  });

  const onSubmit = (data: ClientFormData) => {
    createClientMutation.mutate(data);
  };

  const watchedValues = watch();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/clients')}
          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
        >
          <ArrowLeft className="w-5 h-5" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create New Client</h1>
          <p className="mt-1 text-sm text-gray-500">
            Set up a new AI Answer Bot for a client website
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Form */}
        <div className="bg-white shadow rounded-lg p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Client Name *
                  </label>
                  <input
                    {...register('name')}
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Acme Corporation"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Domain *
                  </label>
                  <input
                    {...register('domain')}
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    placeholder="acme.com"
                  />
                  {errors.domain && (
                    <p className="mt-1 text-sm text-red-600">{errors.domain.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Site Name
                  </label>
                  <input
                    {...register('siteName')}
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Acme Corporation"
                  />
                  {errors.siteName && (
                    <p className="mt-1 text-sm text-red-600">{errors.siteName.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Branding */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Branding</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Primary Color
                  </label>
                  <div className="flex items-center space-x-3">
                    <input
                      {...register('primaryColor')}
                      type="color"
                      className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                    />
                    <input
                      {...register('primaryColor')}
                      type="text"
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                      placeholder="#667eea"
                    />
                  </div>
                  {errors.primaryColor && (
                    <p className="mt-1 text-sm text-red-600">{errors.primaryColor.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Logo URL
                  </label>
                  <input
                    {...register('logo')}
                    type="url"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    placeholder="https://your-site.com/logo.png"
                  />
                  {errors.logo && (
                    <p className="mt-1 text-sm text-red-600">{errors.logo.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Header Text
                  </label>
                  <input
                    {...register('askAnythingText')}
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Ask anything"
                  />
                  {errors.askAnythingText && (
                    <p className="mt-1 text-sm text-red-600">{errors.askAnythingText.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Input Placeholder
                  </label>
                  <input
                    {...register('placeholder')}
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Type your question here..."
                  />
                  {errors.placeholder && (
                    <p className="mt-1 text-sm text-red-600">{errors.placeholder.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => navigate('/clients')}
                className="btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={!isValid || createClientMutation.isPending}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Save className="w-4 h-4 mr-2" />
                {createClientMutation.isPending ? 'Creating...' : 'Create Client'}
              </button>
            </div>
          </form>
        </div>

        {/* Preview */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Preview</h3>
            <Eye className="w-5 h-5 text-gray-400" />
          </div>
          
          <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
            <div className="text-center mb-4">
              <h4 className="font-medium text-gray-900">
                {watchedValues.askAnythingText || 'Ask anything'}
              </h4>
              {watchedValues.logo && (
                <img 
                  src={watchedValues.logo} 
                  alt="Logo preview"
                  className="w-10 h-10 mx-auto mt-2 rounded"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
              )}
            </div>
            
            <div className="mb-4">
              <input
                type="text"
                placeholder={watchedValues.placeholder || 'Type your question here...'}
                className="w-full px-3 py-2 border border-gray-300 rounded-full text-sm"
                style={{ borderColor: watchedValues.primaryColor || '#667eea' }}
                readOnly
              />
            </div>
            
            <div className="space-y-2">
              <div 
                className="p-3 rounded-lg border cursor-pointer text-sm"
                style={{ borderColor: watchedValues.primaryColor || '#667eea' }}
              >
                How can I get started?
              </div>
              <div className="p-3 bg-white rounded-lg border text-sm">
                <div className="font-medium">Sample Page</div>
                <div className="text-gray-500 text-xs">Learn more about our services</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateClient;
