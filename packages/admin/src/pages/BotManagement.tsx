/**
 * Bot management page component for the admin panel.
 * This component provides tools for configuring bot behavior, managing suggestions,
 * testing responses, and monitoring bot performance across all clients.
 */

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Bot, 
  MessageSquare, 
  Plus, 
  Search,
  TestTube,
  Settings,
  TrendingUp
} from 'lucide-react';
import { apiService } from '../services/api';

const BotManagement: React.FC = () => {
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [testQuestion, setTestQuestion] = useState('');

  const { data: clients } = useQuery({
    queryKey: ['clients'],
    queryFn: () => apiService.getClients(1, 100)
  });

  const { data: suggestions } = useQuery({
    queryKey: ['suggestions', selectedClient],
    queryFn: () => apiService.getSuggestions(selectedClient),
    enabled: !!selectedClient
  });

  const handleTestBot = async () => {
    if (!testQuestion.trim() || !selectedClient) return;
    
    // This would test the bot response
    console.log('Testing bot with question:', testQuestion);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Bot Management</h1>
          <p className="mt-1 text-sm text-gray-500">
            Configure and test your AI Answer Bot across all clients
          </p>
        </div>
        <button className="btn-primary">
          <Plus className="w-4 h-4 mr-2" />
          Add Suggestion
        </button>
      </div>

      {/* Client Selection */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Client
            </label>
            <select
              value={selectedClient}
              onChange={(e) => setSelectedClient(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="">Choose a client...</option>
              {clients?.data?.map((client) => (
                <option key={client.id} value={client.id}>
                  {client.name} ({client.domain})
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {selectedClient && (
        <>
          {/* Bot Testing */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center mb-4">
              <TestTube className="w-5 h-5 text-primary-500 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">Test Bot Response</h3>
            </div>
            
            <div className="flex space-x-4">
              <input
                type="text"
                value={testQuestion}
                onChange={(e) => setTestQuestion(e.target.value)}
                placeholder="Enter a test question..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
              />
              <button
                onClick={handleTestBot}
                disabled={!testQuestion.trim()}
                className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Test
              </button>
            </div>
          </div>

          {/* Suggestions Management */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <MessageSquare className="w-5 h-5 text-primary-500 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">Bot Suggestions</h3>
              </div>
              <button className="btn-secondary">
                <Plus className="w-4 h-4 mr-2" />
                Add Suggestion
              </button>
            </div>

            {suggestions && suggestions.length > 0 ? (
              <div className="space-y-3">
                {suggestions.map((suggestion) => (
                  <div key={suggestion.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{suggestion.question}</p>
                      <p className="text-xs text-gray-500">
                        Category: {suggestion.category || 'General'} • Priority: {suggestion.priority}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="text-gray-400 hover:text-gray-600">
                        <Settings className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No suggestions</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Add some suggested questions to help users get started.
                </p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default BotManagement;
