/**
 * Dashboard page component for the admin panel.
 * This component displays an overview of the AI Answer Bot system including
 * client statistics, recent activity, and quick access to key management functions.
 */

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { 
  Users, 
  MessageSquare, 
  ExternalLink, 
  TrendingUp,
  Plus,
  Activity
} from 'lucide-react';
import { apiService } from '../services/api';

const Dashboard: React.FC = () => {
  const { data: clients, isLoading: clientsLoading } = useQuery({
    queryKey: ['clients'],
    queryFn: () => apiService.getClients(1, 10)
  });

  const { data: healthStatus } = useQuery({
    queryKey: ['health'],
    queryFn: () => apiService.healthCheck(),
    refetchInterval: 30000 // Check every 30 seconds
  });

  const stats = [
    {
      name: 'Total Clients',
      value: clients?.total || 0,
      icon: Users,
      color: 'bg-blue-500',
      href: '/clients'
    },
    {
      name: 'Active Bots',
      value: clients?.data?.filter(c => c.config.siteName).length || 0,
      icon: MessageSquare,
      color: 'bg-green-500',
      href: '/bot'
    },
    {
      name: 'Total Interactions',
      value: '1,234', // This would come from analytics
      icon: TrendingUp,
      color: 'bg-purple-500',
      href: '/analytics'
    },
    {
      name: 'System Status',
      value: healthStatus?.status === 'ok' ? 'Healthy' : 'Issues',
      icon: Activity,
      color: healthStatus?.status === 'ok' ? 'bg-green-500' : 'bg-red-500',
      href: '/settings'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Welcome to the AI Answer Bot admin panel
          </p>
        </div>
        <Link
          to="/clients/new"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <Plus className="w-4 h-4 mr-2" />
          New Client
        </Link>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Link
              key={stat.name}
              to={stat.href}
              className="relative bg-white pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow rounded-lg overflow-hidden hover:shadow-md transition-shadow"
            >
              <div>
                <div className={`absolute ${stat.color} rounded-md p-3`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <p className="ml-16 text-sm font-medium text-gray-500 truncate">
                  {stat.name}
                </p>
              </div>
              <div className="ml-16 flex items-baseline pb-6 sm:pb-7">
                <p className="text-2xl font-semibold text-gray-900">
                  {stat.value}
                </p>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Recent Clients */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Recent Clients
            </h3>
            <Link
              to="/clients"
              className="text-sm text-primary-600 hover:text-primary-500"
            >
              View all
            </Link>
          </div>

          {clientsLoading ? (
            <div className="animate-pulse space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : clients?.data?.length ? (
            <div className="space-y-4">
              {clients.data.slice(0, 5).map((client) => (
                <div key={client.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-primary-700">
                        {client.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{client.name}</p>
                      <p className="text-sm text-gray-500">{client.domain}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Link
                      to={`/clients/${client.id}`}
                      className="text-primary-600 hover:text-primary-500"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No clients</h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating your first client.
              </p>
              <div className="mt-6">
                <Link
                  to="/clients/new"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  New Client
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* System Status */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            System Status
          </h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${
                healthStatus?.status === 'ok' ? 'bg-green-400' : 'bg-red-400'
              }`}></div>
              <span className="text-sm text-gray-700">
                API Server: {healthStatus?.status === 'ok' ? 'Online' : 'Offline'}
              </span>
            </div>
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${
                healthStatus?.database === 'connected' ? 'bg-green-400' : 'bg-red-400'
              }`}></div>
              <span className="text-sm text-gray-700">
                Database: {healthStatus?.database === 'connected' ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
          {healthStatus?.timestamp && (
            <p className="mt-2 text-xs text-gray-500">
              Last checked: {new Date(healthStatus.timestamp).toLocaleString()}
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
