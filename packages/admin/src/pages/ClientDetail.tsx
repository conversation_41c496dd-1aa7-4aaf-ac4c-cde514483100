/**
 * Client detail page component for the admin panel.
 * This component displays comprehensive client information including configuration,
 * scraped pages, bot suggestions, and analytics with inline editing capabilities.
 */

import React from 'react';
import { useParams, Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { 
  ArrowLeft, 
  Edit, 
  Copy, 
  ExternalLink,
  Globe,
  MessageSquare,
  BarChart3,
  Settings
} from 'lucide-react';
import { apiService } from '../services/api';
import toast from 'react-hot-toast';

const ClientDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  const { data: client, isLoading } = useQuery({
    queryKey: ['client', id],
    queryFn: () => apiService.getClient(id!),
    enabled: !!id
  });

  const handleCopyApiKey = async () => {
    if (client?.apiKey) {
      try {
        await navigator.clipboard.writeText(client.apiKey);
        toast.success('API key copied to clipboard');
      } catch (error) {
        toast.error('Failed to copy API key');
      }
    }
  };

  const handleCopyEmbedCode = async () => {
    if (client) {
      const embedCode = `<script src="https://cdn.your-domain.com/ai-answer-bot.umd.js"></script>
<script>
  window.AIAnswerBot.init({
    clientId: '${client.id}',
    apiUrl: 'https://your-api-url.com/api',
    primaryColor: '${client.config.primaryColor || '#667eea'}',
    askAnythingText: '${client.config.askAnythingText || 'Ask anything'}',
    logo: '${client.config.logo || ''}',
    placeholder: '${client.config.placeholder || 'Type your question here...'}',
    siteName: '${client.config.siteName || client.name}'
  });
</script>`;

      try {
        await navigator.clipboard.writeText(embedCode);
        toast.success('Embed code copied to clipboard');
      } catch (error) {
        toast.error('Failed to copy embed code');
      }
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-lg shadow">
                <div className="h-6 bg-gray-200 rounded mb-4"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Client not found</h2>
        <p className="text-gray-500 mb-6">The requested client could not be found.</p>
        <Link to="/clients" className="btn-primary">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Clients
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            to="/clients"
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
              {client.config.logo ? (
                <img 
                  src={client.config.logo} 
                  alt={client.name}
                  className="w-10 h-10 rounded"
                />
              ) : (
                <span className="text-lg font-medium text-primary-700">
                  {client.name.charAt(0).toUpperCase()}
                </span>
              )}
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{client.name}</h1>
              <p className="text-sm text-gray-500">{client.domain}</p>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleCopyEmbedCode}
            className="btn-secondary"
          >
            <Copy className="w-4 h-4 mr-2" />
            Copy Embed Code
          </button>
          <Link to={`/clients/${client.id}/edit`} className="btn-primary">
            <Edit className="w-4 h-4 mr-2" />
            Edit Client
          </Link>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <Globe className="w-8 h-8 text-blue-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Domain</p>
              <p className="text-lg font-semibold text-gray-900">{client.domain}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <MessageSquare className="w-8 h-8 text-green-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Suggestions</p>
              <p className="text-lg font-semibold text-gray-900">5</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <ExternalLink className="w-8 h-8 text-purple-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Scraped Pages</p>
              <p className="text-lg font-semibold text-gray-900">12</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <BarChart3 className="w-8 h-8 text-orange-500" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">This Month</p>
              <p className="text-lg font-semibold text-gray-900">1,234</p>
            </div>
          </div>
        </div>
      </div>

      {/* Configuration */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900">Configuration</h3>
          <Settings className="w-5 h-5 text-gray-400" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Basic Settings</h4>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm text-gray-500">Client ID</dt>
                <dd className="text-sm font-mono text-gray-900">{client.id}</dd>
              </div>
              <div>
                <dt className="text-sm text-gray-500">API Key</dt>
                <dd className="flex items-center space-x-2">
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                    {client.apiKey.substring(0, 20)}...
                  </code>
                  <button
                    onClick={handleCopyApiKey}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                </dd>
              </div>
              <div>
                <dt className="text-sm text-gray-500">Created</dt>
                <dd className="text-sm text-gray-900">
                  {new Date(client.createdAt).toLocaleDateString()}
                </dd>
              </div>
            </dl>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Branding</h4>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm text-gray-500">Primary Color</dt>
                <dd className="flex items-center space-x-2">
                  <div 
                    className="w-4 h-4 rounded border"
                    style={{ backgroundColor: client.config.primaryColor || '#667eea' }}
                  ></div>
                  <span className="text-sm text-gray-900">
                    {client.config.primaryColor || '#667eea'}
                  </span>
                </dd>
              </div>
              <div>
                <dt className="text-sm text-gray-500">Header Text</dt>
                <dd className="text-sm text-gray-900">
                  {client.config.askAnythingText || 'Ask anything'}
                </dd>
              </div>
              <div>
                <dt className="text-sm text-gray-500">Site Name</dt>
                <dd className="text-sm text-gray-900">
                  {client.config.siteName || 'Not set'}
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Link
          to={`/clients/${client.id}/pages`}
          className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
        >
          <div className="flex items-center">
            <ExternalLink className="w-8 h-8 text-purple-500" />
            <div className="ml-4">
              <h4 className="text-lg font-medium text-gray-900">Manage Content</h4>
              <p className="text-sm text-gray-500">Add and manage scraped pages</p>
            </div>
          </div>
        </Link>
        
        <Link
          to={`/clients/${client.id}/suggestions`}
          className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
        >
          <div className="flex items-center">
            <MessageSquare className="w-8 h-8 text-green-500" />
            <div className="ml-4">
              <h4 className="text-lg font-medium text-gray-900">Bot Suggestions</h4>
              <p className="text-sm text-gray-500">Configure suggested questions</p>
            </div>
          </div>
        </Link>
        
        <Link
          to={`/analytics?client=${client.id}`}
          className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
        >
          <div className="flex items-center">
            <BarChart3 className="w-8 h-8 text-orange-500" />
            <div className="ml-4">
              <h4 className="text-lg font-medium text-gray-900">View Analytics</h4>
              <p className="text-sm text-gray-500">Monitor usage and performance</p>
            </div>
          </div>
        </Link>
      </div>
    </div>
  );
};

export default ClientDetail;
