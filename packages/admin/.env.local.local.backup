# API Configuration
VITE_API_URL=http://localhost:5000/api
VITE_ADMIN_API_KEY=[0;35m[STEP][0m Creating API environment configuration...
[1;33m[WARNING][0m API .env file already exists, backing up to .env.backup
[0;32m[SUCCESS][0m Created API .env file: packages/api/.env
[0;36m[CONFIG][0m API will run on port 5000
[0;36m[CONFIG][0m Admin API Key: dev-admin-key-1755546153
dev-admin-key-1755546153

# Environment
VITE_NODE_ENV=development

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_SCRAPING=true
VITE_ENABLE_TESTING=true
VITE_ENABLE_DEBUG=true

# External Services (optional - for production)
VITE_SENTRY_DSN=your-sentry-dsn-here
VITE_GOOGLE_ANALYTICS_ID=your-ga-id-here

# Development Settings
VITE_API_TIMEOUT=10000
VITE_REFRESH_INTERVAL=30000
