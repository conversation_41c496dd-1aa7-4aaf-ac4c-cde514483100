# @ai-bot/admin

The AI Answer Bot Admin Panel is a React TypeScript application that provides a comprehensive management interface for the AI chatbot system. It allows administrators to manage clients, configure bot settings, monitor analytics, and customize the embed widget appearance.

## Features

- **Client Management**: Create, edit, and delete client configurations
- **Bot Configuration**: Manage suggestions, scraped content, and responses
- **Brand Customization**: Set logos, colors, and messaging per client
- **Content Scraping**: Add and manage website URLs for content extraction
- **Analytics Dashboard**: Monitor bot usage and user interactions
- **Real-time Updates**: Live data with React Query integration
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Installation

```bash
cd packages/admin
npm install
```

## Configuration

Create environment configuration:

```bash
cp .env.example .env.local
```

Key environment variables:
- `VITE_API_URL` - Backend API endpoint
- `VITE_ADMIN_API_KEY` - Admin authentication key

## Development

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Type checking
npm run type-check

# Linting
npm run lint
```

## Project Structure

```
src/
├── components/     # Reusable UI components
├── pages/         # Route-based page components
├── hooks/         # Custom React hooks
├── services/      # API communication layer
├── types/         # TypeScript type definitions
├── utils/         # Utility functions
└── App.tsx        # Main application component
```

## Key Components

### Dashboard
- System overview and health monitoring
- Quick access to key management functions
- Recent activity and statistics

### Client Management
- List all clients with search and filtering
- Create new clients with configuration
- Edit existing client settings and branding
- Manage API keys and access controls

### Bot Configuration
- Manage suggested questions and categories
- Configure scraped content sources
- Set up automated content updates
- Test bot responses and behavior

### Analytics
- Track user interactions and engagement
- Monitor popular questions and content
- Generate usage reports and insights
- Export data for external analysis

## API Integration

The admin panel communicates with the backend API for:
- Client CRUD operations
- Content management and scraping
- Bot configuration and testing
- Analytics data retrieval
- System health monitoring

## Authentication

Admin access is controlled via API key authentication:
- All requests include `X-Admin-Key` header
- Keys are configured in environment variables
- Failed authentication redirects to error page

## Deployment

The admin panel builds to static files for deployment:

```bash
# Build for production
npm run build

# Deploy to S3/CloudFront
npm run deploy
```

## Customization

The admin panel uses Tailwind CSS for styling:
- Consistent design system with primary color theming
- Responsive breakpoints for all screen sizes
- Dark mode support (configurable)
- Custom component library for consistency

## Performance

- React Query for efficient data fetching and caching
- Code splitting for optimal bundle sizes
- Lazy loading for route-based components
- Optimized images and assets
