# Contributing to AI Answer Bot Demo

Thank you for your interest in contributing to the AI Answer Bot Demo project! This document provides guidelines and information for contributors.

## Development Setup

### Prerequisites
- Node.js 18+ and npm 9+
- PostgreSQL 12+ (for local development)
- AWS CLI (for deployment)
- Git

### Local Development

1. **Clone the repository:**
```bash
git clone <repository-url>
cd ai-answer-bot-demo
```

2. **Install dependencies:**
```bash
npm install
```

3. **Set up environment variables:**
```bash
# API
cp packages/api/.env.example packages/api/.env
# Edit packages/api/.env with your database credentials

# Admin Panel
cp packages/admin/.env.example packages/admin/.env.local
# Edit packages/admin/.env.local with your API settings
```

4. **Set up database:**
```bash
# Create database
createdb ai_answer_bot

# Run migrations
npm run db:migrate

# Seed with demo data (optional)
npm run db:seed
```

5. **Start development servers:**
```bash
npm run dev
```

This starts:
- API server on http://localhost:5000
- Admin panel on http://localhost:5001
- Embed widget demo on http://localhost:5175

## Project Structure

```
ai-answer-bot-demo/
├── packages/
│   ├── embed/          # React embed widget
│   ├── admin/          # React admin panel
│   └── api/            # Node.js API backend
├── scripts/            # AWS deployment scripts
├── DEPLOYMENT.md       # Deployment instructions
├── ARCHITECTURE.md     # Technical architecture
└── README.md          # Project overview
```

## Code Standards

### TypeScript
- Use strict TypeScript configuration
- Provide type definitions for all functions and components
- Use interfaces for object shapes
- Avoid `any` types

### React Components
- Use functional components with hooks
- Implement proper prop types
- Use CSS Modules or Tailwind for styling
- Include JSDoc comments for component purpose

### API Development
- Follow RESTful conventions
- Use proper HTTP status codes
- Implement request validation with Joi
- Include comprehensive error handling

### Database
- Use parameterized queries to prevent SQL injection
- Include proper indexes for performance
- Use transactions for multi-step operations
- Follow PostgreSQL naming conventions

## Testing

### Running Tests
```bash
# Run all tests
npm test

# Run tests for specific package
npm test --workspace=@ai-bot/api
npm test --workspace=@ai-bot/admin
npm test --workspace=@ai-bot/embed
```

### Test Requirements
- **API**: Unit tests for services and models
- **Components**: React Testing Library for UI components
- **Integration**: End-to-end API testing
- **Coverage**: Maintain >80% code coverage

### Writing Tests
- Test files should be co-located with source files
- Use descriptive test names
- Include both positive and negative test cases
- Mock external dependencies

## Pull Request Process

### Before Submitting
1. **Run tests**: Ensure all tests pass
2. **Type checking**: Run `npm run type-check` in all packages
3. **Linting**: Run `npm run lint` and fix any issues
4. **Build**: Verify all packages build successfully
5. **Documentation**: Update relevant documentation

### PR Guidelines
1. **Title**: Use clear, descriptive titles
2. **Description**: Explain what changes were made and why
3. **Testing**: Describe how the changes were tested
4. **Breaking Changes**: Clearly mark any breaking changes
5. **Screenshots**: Include for UI changes

### Review Process
- All PRs require at least one review
- Address all review comments
- Ensure CI/CD pipeline passes
- Squash commits before merging

## Coding Guidelines

### File Organization
- One component per file
- Use descriptive file names
- Group related files in directories
- Include index.ts files for clean imports

### Naming Conventions
- **Files**: PascalCase for components, camelCase for utilities
- **Variables**: camelCase
- **Constants**: UPPER_SNAKE_CASE
- **Types**: PascalCase interfaces
- **CSS Classes**: kebab-case or camelCase (CSS Modules)

### Documentation
- Include JSDoc comments for all public functions
- Add file-level comments explaining purpose
- Update README files for significant changes
- Document API endpoints and parameters

## Issue Reporting

### Bug Reports
Include:
- Steps to reproduce
- Expected vs actual behavior
- Environment details (OS, browser, Node.js version)
- Error messages and stack traces
- Screenshots if applicable

### Feature Requests
Include:
- Clear description of the feature
- Use cases and benefits
- Proposed implementation approach
- Any breaking changes

## Security

### Reporting Security Issues
- **Do not** create public issues for security vulnerabilities
- Email security concerns to [security-email]
- Include detailed description and reproduction steps

### Security Guidelines
- Never commit secrets or API keys
- Use environment variables for configuration
- Validate all user inputs
- Follow OWASP security guidelines
- Keep dependencies updated

## Release Process

### Versioning
- Follow Semantic Versioning (SemVer)
- Update package.json versions
- Create git tags for releases
- Update CHANGELOG.md

### Release Checklist
1. Update version numbers
2. Update documentation
3. Run full test suite
4. Build all packages
5. Test deployment process
6. Create release notes
7. Deploy to production

## Getting Help

- **Documentation**: Check README files and architecture docs
- **Issues**: Search existing issues before creating new ones
- **Discussions**: Use GitHub Discussions for questions
- **Code Review**: Ask for help in PR comments

## License

By contributing to this project, you agree that your contributions will be licensed under the same license as the project (MIT License).
