{"name": "ai-answer-bot-demo", "version": "1.0.0", "description": "AI Answer Bot Demo - A customizable embed widget with admin panel and API backend", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "concurrently \"npm run dev:api\" \"npm run dev:embed\" \"npm run dev:admin\"", "dev:api": "npm run dev --workspace=@ai-bot/api", "dev:embed": "npm run dev --workspace=@ai-bot/embed", "dev:admin": "npm run dev --workspace=@ai-bot/admin", "setup-and-run": "bash -c 'source scripts/utils/setup-and-run.sh && setup_and_run_all'", "build": "npm run build --workspaces", "build:api": "npm run build --workspace=@ai-bot/api", "build:embed": "npm run build --workspace=@ai-bot/embed", "build:admin": "npm run build --workspace=@ai-bot/admin", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "set:credentials": "cp ~/.aws/projects/internal ~/.aws/credentials", "db:setup": "scripts/utils/setup-local-db.sh", "db:reset": "scripts/utils/reset-local-db.sh", "db:test": "scripts/utils/test-db-connection.sh", "db:migrate": "npm run migrate --workspace=@ai-bot/api", "db:seed": "npm run seed --workspace=@ai-bot/api", "deploy:frontend": "npm run set:credentials; scripts/deployment/frontend/deploy-frontend.sh", "deploy:backend": "npm run set:credentials; scripts/deployment/backend/deploy-backend.sh", "deploy:full": "npm run set:credentials; npm run deploy:backend && npm run deploy:frontend", "deploy:frontend:update": "npm run set:credentials; scripts/deployment/frontend/update-frontend.sh", "deploy:backend:update": "npm run set:credentials; scripts/deployment/backend/update-backend.sh", "deploy:frontend:status": "npm run set:credentials; scripts/deployment/frontend/frontend-status.sh", "deploy:backend:status": "npm run set:credentials; scripts/deployment/backend/backend-status.sh", "status:all": "npm run deploy:frontend:status && npm run deploy:backend:status", "switch:api": "scripts/utils/switch-api.sh", "logs:tail": "npm run set:credentials; scripts/connect/backend/tail-lambda-logs.sh", "backend:connect": "npm run set:credentials; scripts/connect/backend/connect-backend.sh", "lambda:env": "npm run set:credentials; scripts/connect/backend/update-lambda-env.sh"}, "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}