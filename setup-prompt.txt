Let's create the prompt for generating an answer bot:

Mono-repo.

The concept is a bot that's presented as an embed code like intercom. The styling of the bot is modern with subtle white / offwhite gradient. 

Embed:

Top-left: Ask anything
Top Center: Logo
Under Logo: field allowing the customer to ask anything
Then:
A few suggestions based on what's been scraped show up in the form of questions the user can quick-ask. 
There are also link previews that show up underneath the suggestions for pages on the site they can click to with a line explaining what that page is / why you would go there.

---

For the purpose of this demo, the embed sits infont of a screenshot of the website that gets scraped which changes for mobile and desktop.
Placeholder if none provided.

---

Primary color is customizable based on API settings along with 'ask anything' text, logo and site information tied to a 'client' object.

React Typescript with Vite and CSS modules

---

Needs a NodeJS Typescript API connected to a Postgres DB.

---

Needs an Admin Panel for viewing a list of clients, adding a link to be scraped for a client, setting logo, color theme and 'Ask anything text'.
Also allow setting placeholder 

React Typescript with Vite and CSS modules

---

Each file must have 1 short paragraph explanation on the file's purpose. Each repo in the mono-repo needs a README.

---

Scripts that should be generated:

Setup API Lambda and Gateway servers - create if not exists
Setup RDS DB - create if not exists
Setup Frontend S3, Cloudfront - create if not exists
S3 must be locked with only access given by cloudfront

Deploy API
Sync DB structure
Deploy Frontend