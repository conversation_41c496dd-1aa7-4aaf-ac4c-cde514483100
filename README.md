# AI Answer Bot Demo

A comprehensive mono-repo containing an AI-powered answer bot system with customizable embed widget, admin panel, and backend API. The system allows businesses to deploy intelligent chatbots on their websites with scraped content suggestions and customizable branding.

## Architecture Overview

This mono-repo consists of three main packages:

- **`@ai-bot/embed`** - React TypeScript embed widget for websites
- **`@ai-bot/admin`** - React TypeScript admin panel for client management
- **`@ai-bot/api`** - Node.js TypeScript API backend with PostgreSQL

## Features

### Embed Widget
- Modern white/off-white gradient styling
- Customizable branding (logo, colors, text)
- Smart suggestions based on scraped content
- Link previews with explanations
- Mobile and desktop responsive
- Easy integration via embed code

### Admin Panel
- Client management interface
- Website scraping configuration
- Brand customization tools
- Analytics and monitoring
- Content management

### API Backend
- RESTful API with TypeScript
- PostgreSQL database integration
- Client authentication and management
- Content scraping and processing
- Scalable AWS Lambda deployment

## Quick Start

```bash
# Install dependencies
npm install

# Start development servers
npm run dev

# Build all packages
npm run build

# Deploy to AWS
npm run deploy:setup
npm run deploy:api
npm run deploy:frontend
```

## Infrastructure

The system is designed to deploy on AWS with:
- **API**: Lambda functions with API Gateway
- **Database**: RDS PostgreSQL
- **Frontend**: S3 + CloudFront distribution
- **Security**: CloudFront-only S3 access

## Development

Each package has its own README with detailed setup instructions. The mono-repo uses npm workspaces for dependency management and shared tooling.

## License

MIT License - See individual package licenses for details.
