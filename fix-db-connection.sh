#!/bin/bash

# Quick fix for PostgreSQL database connection issues
# This script diagnoses and fixes common PostgreSQL connection problems on macOS

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

echo "🔧 PostgreSQL Connection Fix"
echo "============================"
echo

# Configuration
DB_NAME="ai_answer_bot"
current_user=$(whoami)

log_info "Current user: $current_user"
log_info "Target database: $DB_NAME"

# Check PostgreSQL status
log_info "Checking PostgreSQL status..."
if brew services list | grep postgresql | grep started > /dev/null; then
    log_success "PostgreSQL service is running"
else
    log_warning "PostgreSQL service not running. Starting..."
    brew services start postgresql@16 || brew services start postgresql@17 || brew services start postgresql
fi

# Check if database exists
log_info "Checking if database exists..."
if psql -d ai_answer_bot -tAc "SELECT 1 FROM pg_database WHERE datname='$DB_NAME'" | grep -q 1; then
    log_success "Database '$DB_NAME' exists"
else
    log_warning "Database '$DB_NAME' does not exist. Creating..."
    createdb $DB_NAME
    if [ $? -eq 0 ]; then
        log_success "Database '$DB_NAME' created successfully"
    else
        log_error "Failed to create database"
        exit 1
    fi
fi

# Test connection with current user (no password)
log_info "Testing connection with current user..."

# First, let's see what databases we can connect to
log_info "Available databases:"
psql -d ai_answer_bot -tAc "SELECT datname FROM pg_database WHERE datistemplate = false;" 2>/dev/null || echo "Could not list databases"

# Test connection to postgres database first
log_info "Testing connection to postgres database..."
if psql -d ai_answer_bot -c "SELECT version();" > /dev/null 2>&1; then
    log_success "Can connect to postgres database"

    # Now test connection to our target database
    log_info "Testing connection to $DB_NAME database..."
    if psql -d $DB_NAME -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "Connection successful with current user ($current_user)"

        # Update .env file to use current user
        if [ -f "packages/api/.env" ]; then
            log_info "Updating .env file to use current user..."
            sed -i.backup "s/DB_USER=.*/DB_USER=$current_user/" packages/api/.env
            sed -i.backup "s/DB_PASSWORD=.*/DB_PASSWORD=/" packages/api/.env
            log_success "Updated packages/api/.env"
        fi

        echo
        log_success "✅ Database connection fixed!"
        echo
        echo "Updated configuration:"
        echo "• Database: $DB_NAME"
        echo "• User: $current_user"
        echo "• Password: (none needed)"
        echo
        echo "You can now run: npm run setup-and-run"

    else
        log_error "Cannot connect to $DB_NAME database"
        log_info "Trying to recreate the database..."

        # Drop and recreate the database
        dropdb $DB_NAME 2>/dev/null || true
        createdb $DB_NAME

        if psql -d $DB_NAME -c "SELECT 1;" > /dev/null 2>&1; then
            log_success "Database recreated and connection successful!"

            # Update .env file
            if [ -f "packages/api/.env" ]; then
                log_info "Updating .env file..."
                sed -i.backup "s/DB_USER=.*/DB_USER=$current_user/" packages/api/.env
                sed -i.backup "s/DB_PASSWORD=.*/DB_PASSWORD=/" packages/api/.env
                log_success "Updated packages/api/.env"
            fi

            echo
            log_success "✅ Database connection fixed!"
            echo
            echo "Updated configuration:"
            echo "• Database: $DB_NAME"
            echo "• User: $current_user"
            echo "• Password: (none needed)"
            echo
            echo "You can now run: npm run setup-and-run"
        else
            log_error "Still cannot connect after recreating database"
        fi
    fi

else
    log_error "Cannot connect to PostgreSQL at all"
    echo
    echo "Manual troubleshooting steps:"
    echo "1. Check PostgreSQL is running: brew services list | grep postgresql"
    echo "2. Try connecting manually: psql -d ai_answer_bot"
    echo "3. Check PostgreSQL logs: brew services info postgresql"
    echo "4. Restart PostgreSQL: brew services restart postgresql"
    echo "5. Check PostgreSQL config: psql -d ai_answer_bot -c 'SHOW config_file;'"
fi
