#!/bin/bash

# Reset Local Database Script for AI Answer Bot Demo
# This script resets the local database, runs migrations, and optionally seeds data

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_NAME="ai_answer_bot"
DB_USER="ai_answer_bot_user"
DB_PASSWORD="password"
DB_HOST="localhost"
DB_PORT="5432"

# Function to print colored output
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to test database connection
test_connection() {
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "SELECT 1;" &> /dev/null; then
        return 0
    else
        return 1
    fi
}

# Function to drop and recreate database
reset_database() {
    log_info "Resetting database '$DB_NAME'..."
    
    # Drop database if exists
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "DROP DATABASE IF EXISTS $DB_NAME;" 2>/dev/null
    
    # Create database
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d postgres -c "CREATE DATABASE $DB_NAME;" 2>/dev/null
    
    log_success "Database '$DB_NAME' reset successfully"
}

# Function to run migrations
run_migrations() {
    log_info "Running database migrations..."
    
    cd packages/api
    npm run migrate
    cd ../..
    
    log_success "Database migrations completed"
}

# Function to seed database
seed_database() {
    log_info "Seeding database with demo data..."
    
    cd packages/api
    npm run seed
    cd ../..
    
    log_success "Database seeded with demo data"
}

# Function to show database info
show_database_info() {
    log_info "Fetching database information..."
    
    echo
    echo "=== DATABASE TABLES ==="
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
        SELECT table_name, 
               (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) as columns
        FROM information_schema.tables t
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name;
    " 2>/dev/null
    
    echo
    echo "=== SAMPLE DATA COUNTS ==="
    PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "
        SELECT 'clients' as table_name, COUNT(*) as row_count FROM clients
        UNION ALL
        SELECT 'scraped_pages', COUNT(*) FROM scraped_pages
        UNION ALL
        SELECT 'bot_suggestions', COUNT(*) FROM bot_suggestions
        UNION ALL
        SELECT 'analytics', COUNT(*) FROM analytics
        UNION ALL
        SELECT 'bot_responses', COUNT(*) FROM bot_responses
        ORDER BY table_name;
    " 2>/dev/null
}

# Main function
main() {
    echo "🔄 AI Answer Bot - Reset Local Database"
    echo "====================================="
    echo
    
    # Test connection
    if ! test_connection; then
        log_error "Cannot connect to PostgreSQL. Please ensure:"
        echo "  1. PostgreSQL is running"
        echo "  2. User '$DB_USER' exists with password '$DB_PASSWORD'"
        echo "  3. User has database creation privileges"
        echo
        echo "Run 'npm run db:setup' for full database setup."
        exit 1
    fi
    
    log_success "Connected to PostgreSQL"
    
    # Confirm reset
    echo "⚠️  This will completely reset the '$DB_NAME' database!"
    echo "   All existing data will be lost."
    echo
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Database reset cancelled"
        exit 0
    fi
    
    # Reset database
    reset_database
    
    # Run migrations
    run_migrations
    
    # Ask about seeding
    echo
    read -p "Would you like to seed the database with demo data? (Y/n): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Nn]$ ]]; then
        seed_database
        show_database_info
    fi
    
    echo
    log_success "🎉 Database reset completed!"
    echo
    echo "=== NEXT STEPS ==="
    echo "1. Start the API server: npm run dev:api"
    echo "2. Visit health check: http://localhost:3004/health"
    echo "3. Start frontend: npm run dev:admin"
    echo
    echo "=== USEFUL COMMANDS ==="
    echo "Connect to database: psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME"
    echo "View tables: \\dt"
    echo "View client data: SELECT name, domain FROM clients;"
}

# Run main function
main "$@"
