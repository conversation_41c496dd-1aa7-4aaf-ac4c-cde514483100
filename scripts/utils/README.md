# Utility Scripts

This directory contains utility scripts for the AI Answer Bot Demo project.

## 🔧 Available Scripts

### `setup-local-db.sh`
Complete local database setup including PostgreSQL installation, database creation, migrations, and seeding.

**Usage:**
```bash
npm run db:setup

# What it does:
# 1. Checks if PostgreSQL is installed (installs if needed)
# 2. Creates database and user
# 3. Creates .env file with database credentials
# 4. Runs migrations to create tables
# 5. Optionally seeds demo data
```

**Features:**
- **Cross-platform**: Works on macOS (Homebrew) and Linux (apt/yum)
- **Automatic installation**: Installs PostgreSQL if not found
- **Environment setup**: Creates proper .env file
- **Demo data**: Seeds database with sample clients and data

### `reset-local-db.sh`
Reset existing database (for users who already have PostgreSQL installed).

**Usage:**
```bash
npm run db:reset

# What it does:
# 1. Drops and recreates the database
# 2. Runs migrations
# 3. Optionally seeds demo data
# 4. Shows database information
```

**Use cases:**
- Reset database to clean state
- Apply schema changes during development
- Refresh demo data

### `switch-api.sh`
Switch between local development API and deployed AWS API endpoints.

**Usage:**
```bash
npm run switch:api [local|deployed|status]

# Examples
npm run switch:api local     # Use localhost:3004 API
npm run switch:api deployed  # Use AWS API Gateway
npm run switch:api status    # Check current configuration
```

**What it does:**
- Updates `.env.local` with the appropriate `VITE_API_BASE_URL`
- Automatically detects deployed API Gateway URL if AWS CLI is configured
- Provides status information about current configuration

### `tail-logs.sh`
Wrapper script for tailing Lambda logs.

**Usage:**
```bash
npm run logs:tail [options]

# Examples
npm run logs:tail           # Tail logs from last 10 minutes
npm run logs:tail -- -f 1h  # Tail logs from last hour
npm run logs:tail -- --help # Show all options
```

**What it does:**
- Provides easy access to Lambda log tailing functionality
- Forwards all arguments to the actual log tailing script
- Automatically sets up AWS credentials

## 🔗 Related Scripts

These utility scripts work with other scripts in the project:

- **Deployment scripts**: `scripts/deployment/` - Deploy and manage AWS infrastructure
- **Connection scripts**: `scripts/connect/` - Connect to and manage deployed backend
- **Configuration**: `scripts/deployment/shared/config.sh` - Central configuration

## 📝 Notes

- All scripts that interact with AWS automatically run `npm run set:credentials` when called via npm scripts
- Use npm scripts instead of calling scripts directly for best results
- Scripts are designed to work from the project root directory
