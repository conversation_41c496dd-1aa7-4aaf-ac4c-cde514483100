# Connection Scripts

This directory contains scripts for connecting to and testing various components of the AI Chatbot system.

## Directory Structure

```
scripts/connect/
├── README.md                    # This file
└── backend/                     # Backend connection scripts
    ├── README.md               # Backend-specific documentation
    ├── connect-backend.sh      # Main backend connection script
    ├── test-backend-connection.js  # Backend testing script
    └── update-lambda-env.sh    # Lambda environment variable updater
```

## Backend Connection Scripts

The `backend/` directory contains comprehensive scripts for:

- **Local Development**: Start, check, and test local backend server
- **Deployed Backend**: Check AWS Lambda/API Gateway status and health
- **Environment Management**: Update Lambda environment variables from local `.env`
- **Connection Testing**: Validate backend endpoints and functionality

### Quick Commands

```bash
# Check backend status
npm run connect:local        # Local backend
npm run connect:deployed     # Deployed backend

# Start local backend
npm run connect:start

# Test backend connection
npm run test:backend

# Update Lambda environment variables
npm run update:lambda-env
```

## Integration with Global Functions

All connection scripts use the shared global functions from `scripts/deployment/shared/global.sh`:

- **Consistent Logging**: Standardized `[INFO]`, `[SUCCESS]`, `[WARNING]`, `[ERROR]` messages
- **AWS Utilities**: Shared AWS CLI validation and resource checking
- **Configuration Management**: Centralized environment variable handling
- **Error Handling**: Robust error handling and cleanup procedures

## Features

### 🏠 Local Development Support
- Start local backend server with dependency checking
- Health check validation for local endpoints
- Port availability and configuration validation

### ☁️ Deployed Backend Support  
- AWS Lambda function status and configuration
- API Gateway endpoint validation and testing
- CloudWatch logs integration for debugging
- Environment variable synchronization

### 🧪 Comprehensive Testing
- Health endpoint validation (`/api/health`)
- Chat endpoint functionality testing (`/api/chat`)
- Response format validation
- Connection diagnostics and troubleshooting

### 🔧 Environment Management
- Sync local `.env` files to Lambda environment variables
- Validate environment variable configuration
- Test updated configurations automatically

## Backward Compatibility

The original scripts in the root `scripts/` directory are maintained as backward compatibility wrappers:

- `scripts/connect-backend.sh` → `scripts/connect/backend/connect-backend.sh`
- `scripts/test-backend-connection.js` → `scripts/connect/backend/test-backend-connection.js`
- `scripts/update-lambda-env.sh` → `scripts/connect/backend/update-lambda-env.sh`

This ensures existing workflows continue to work while encouraging migration to the new organized structure.

## Usage Examples

### Check All Connection Options
```bash
npm run connect:backend
# Shows comprehensive help with all available commands
```

### Local Development Workflow
```bash
# Start local backend
npm run connect:start

# In another terminal, test the connection
npm run test:backend
```

### Deployed Backend Workflow
```bash
# Check deployment status
npm run connect:deployed

# Update environment variables if needed
npm run update:lambda-env

# Test the deployed backend
npm run test:backend test https://your-api-gateway-url.com
```

### Troubleshooting Workflow
```bash
# Check local backend
npm run connect:local

# Check deployed backend
npm run connect:deployed

# Test specific endpoint
./scripts/connect/backend/connect-backend.sh test https://custom-url.com
```

## Configuration

### Environment Variables
Scripts use these configuration variables with sensible defaults:

```bash
LAMBDA_FUNCTION_NAME="${LAMBDA_FUNCTION_NAME:-quote-gen-bloodandtreasure}"
API_GATEWAY_NAME="${API_GATEWAY_NAME:-quote-gen-bloodandtreasure-api}"
API_GATEWAY_STAGE="${API_GATEWAY_STAGE:-prod}"
AWS_REGION="${AWS_REGION:-us-east-1}"
BACKEND_DIR="${BACKEND_DIR:-backend}"
```

### Local Backend Configuration
Edit `backend/.env`:
```env
PORT=5001
OPENAI_API_KEY=your-openai-api-key
# Add other environment variables as needed
```

## Future Extensions

This directory structure is designed to accommodate additional connection scripts for other system components:

- `frontend/` - Frontend connection and testing scripts
- `database/` - Database connection and migration scripts  
- `monitoring/` - Health monitoring and alerting scripts
- `integration/` - End-to-end integration testing scripts

## Support

For issues with connection scripts:

1. **Check Script Output**: All scripts provide detailed logging with color-coded messages
2. **Verify Prerequisites**: Ensure Node.js, AWS CLI, and other dependencies are installed
3. **Review Configuration**: Check environment variables and configuration files
4. **Use Global Functions**: Leverage the shared utilities for consistent behavior
5. **Check Documentation**: Refer to the specific README files in each subdirectory

The connection scripts are designed to be self-documenting with comprehensive help messages and error reporting.
