ps aux | grep node | grep Desktop | grep -v Repd | awk '{print $2}' | xargs kill -9

psql postgres
-- Create the database
CREATE DATABASE ai_answer_bot;
-- Create a user (optional - you can use your current user)
CREATE USER ai_answer_bot_user WITH PASSWORD 'password';
-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE ai_answer_bot TO ai_answer_bot_user;
-- Make the user a superuser (for development)
ALTER USER ai_answer_bot_user CREATEDB SUPERUSER;
-- Grant ownership of the schema to the user
ALTER SCHEMA public OWNER TO ai_answer_bot_user;

-- Or, if you don't want to change ownership, grant full privileges
GRANT ALL PRIVILEGES ON SCHEMA public TO ai_answer_bot_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ai_answer_bot_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ai_answer_bot_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO ai_answer_bot_user;

-- Ensure default privileges for newly created tables/functions
ALTER DEFAULT PRIVILEGES IN SCHEMA public
    GRANT ALL ON TABLES TO ai_answer_bot_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public
    GRANT ALL ON SEQUENCES TO ai_answer_bot_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public
    GRANT ALL ON FUNCTIONS TO ai_answer_bot_user;
-- Exit PostgreSQL
\q